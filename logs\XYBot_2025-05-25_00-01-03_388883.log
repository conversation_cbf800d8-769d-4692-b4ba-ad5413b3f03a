2025-05-25 00:01:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:01:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:02:20 | INFO | 收到文本消息: 消息ID:2049718583 来自:27852221909@chatroom 发送人:wxid_se4umlaxvz1p21 @:[] 内容:可以了
2025-05-25 00:02:20 | DEBUG | [ChatSummary] 群聊未启用功能，不保存消息: 27852221909@chatroom
2025-05-25 00:02:20 | DEBUG | 收到消息: 可以了
2025-05-25 00:02:20 | DEBUG | 处理消息内容: '可以了'
2025-05-25 00:02:20 | DEBUG | 消息内容 '可以了' 不匹配任何命令，忽略
2025-05-25 00:06:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:06:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:09:21 | INFO | 收到表情消息: 消息ID:2085065504 来自:48097389945@chatroom 发送人:wxid_qzduakk7yrry22 MD5:aa7c0a9cfe4eaae8cd209f3ca76ebe21 大小:28024
2025-05-25 00:09:33 | INFO | 收到图片消息: 消息ID:2103860170 来自:48097389945@chatroom 发送人:wxid_qzduakk7yrry22 XML:<?xml version="1.0"?><msg><img aeskey="1164d5713b9c8a367c2bc6ff96feb286" encryver="1" cdnthumbaeskey="1164d5713b9c8a367c2bc6ff96feb286" cdnthumburl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef44042462363436656362392d366661322d346166642d626236632d626161653438646136316430020405250a020201000405004c53d900" cdnthumblength="4725" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef44042462363436656362392d366661322d346166642d626236632d626161653438646136316430020405250a020201000405004c53d900" length="10689" md5="415905c3db76686ccac69cfc63891493" hevc_mid_size="10689" originsourcemd5="55046db313426b42a495d5a1836d641f"><secHashInfoBase64>eyJwaGFzaCI6IjUxMTExMDExNTA1MDEwMTAiLCJwZHFIYXNoIjoiMzU1YTRjYmNjYjQ1N2FhMWE3NmYzYzkyYzYwNzNhMWJlMmVmM2RmYTQ1MDRjYmUxZjkyMWVmZWExNDEwOTQwNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-05-25 00:09:33 | INFO | [ImageEcho] 收到图片消息: 来自 48097389945@chatroom, 发送人 wxid_qzduakk7yrry22
2025-05-25 00:09:33 | DEBUG | [ImageEcho] 成功保存群 48097389945@chatroom 的图片数据
2025-05-25 00:09:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-05-25 00:09:34 | INFO | 收到图片消息: 消息ID:259830336 来自:48097389945@chatroom 发送人:wxid_qzduakk7yrry22 XML:<?xml version="1.0"?><msg><img aeskey="3cd2c844aaf5a2d2789583872477bf1a" encryver="1" cdnthumbaeskey="3cd2c844aaf5a2d2789583872477bf1a" cdnthumburl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef45042431343038306661342d383739332d343531312d393165312d613234623235346134643531020405290a020201000405004c57c100" cdnthumblength="4238" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef45042431343038306661342d383739332d343531312d393165312d613234623235346134643531020405290a020201000405004c57c100" length="8589" md5="40c630fb7fd43e17ef3cb3a35d03ad26" hevc_mid_size="8589" originsourcemd5="52a7aeaf45381f44e38777dd63864255"><secHashInfoBase64>eyJwaGFzaCI6IjUxMDAxMTUwNTAxMDUwMTAiLCJwZHFIYXNoIjoiYzhmMjZjYjYxZjY5NjI1YmYyMWE0ZWRiN2UwNjkzYzk2NTNhMjhmNDljZTQ2N2E0Mjk2MWNjM2UzNzg1MTNjMSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-05-25 00:09:34 | INFO | [ImageEcho] 收到图片消息: 来自 48097389945@chatroom, 发送人 wxid_qzduakk7yrry22
2025-05-25 00:09:34 | DEBUG | [ImageEcho] 成功保存群 48097389945@chatroom 的图片数据
2025-05-25 00:09:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-05-25 00:09:34 | INFO | 收到图片消息: 消息ID:********* 来自:48097389945@chatroom 发送人:wxid_qzduakk7yrry22 XML:<?xml version="1.0"?><msg><img aeskey="8c741a4a6bb360662f503be8a482b666" encryver="1" cdnthumbaeskey="8c741a4a6bb360662f503be8a482b666" cdnthumburl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef46042430323961353365632d393762372d346437332d616633392d393538393862383064356135020405250a020201000405004c543d00" cdnthumblength="2254" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef46042430323961353365632d393762372d346437332d616633392d393538393862383064356135020405250a020201000405004c543d00" length="4878" md5="541704c1a2d510697ddb1f76ccd10e31" hevc_mid_size="4878" originsourcemd5="0ee1501db1b60385726bb7395dc0a5a1"><secHashInfoBase64>eyJwaGFzaCI6IjUwNTAwMDAwMDA1MDUwMDAiLCJwZHFIYXNoIjoiMTk5MzMzOWI0ZTFiNGU0YjRiNGI1YjRlOGM0ZWU2NmNiNGU0YjFlNGE0YjRlNGI4OTFlNjk5YzY0NjE5NGY1YiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-05-25 00:09:34 | INFO | [ImageEcho] 收到图片消息: 来自 48097389945@chatroom, 发送人 wxid_qzduakk7yrry22
2025-05-25 00:09:34 | DEBUG | [ImageEcho] 成功保存群 48097389945@chatroom 的图片数据
2025-05-25 00:09:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-05-25 00:09:36 | INFO | 收到图片消息: 消息ID:447122844 来自:48097389945@chatroom 发送人:wxid_qzduakk7yrry22 XML:<?xml version="1.0"?><msg><img aeskey="d801cb52b05f172bb4b4c0efa4621024" encryver="1" cdnthumbaeskey="d801cb52b05f172bb4b4c0efa4621024" cdnthumburl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef47042430646166623763322d393037302d346439372d613666652d353636393963343762613238020405250a020201000405004c53d900" cdnthumblength="1782" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020484fb369802032f9f690204ec50f67b02046831ef47042430646166623763322d393037302d346439372d613666652d353636393963343762613238020405250a020201000405004c53d900" length="4112" md5="b2889411acef941be2cb22128ba54bd3" hevc_mid_size="4112" originsourcemd5="219aa4e8e656430ce4234c4034f55c3e"><secHashInfoBase64>eyJwaGFzaCI6IjEwMDAwMDUwMTAwMDEwMTAiLCJwZHFIYXNoIjoiMjVlMTY2NzNmNjVhZDI1ZWQ5OWUwOWE1MGRhMTI1ZTFiNjVhZjY1ZWQyNWVjOThjMDlhMTBkYTEyZGExZjI1ZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-05-25 00:09:36 | INFO | [ImageEcho] 收到图片消息: 来自 48097389945@chatroom, 发送人 wxid_qzduakk7yrry22
2025-05-25 00:09:36 | DEBUG | [ImageEcho] 成功保存群 48097389945@chatroom 的图片数据
2025-05-25 00:09:36 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-05-25 00:10:06 | INFO | 收到表情消息: 消息ID:430853756 来自:48097389945@chatroom 发送人:wxid_e3o8s2nf9u2o22 MD5:d1e57a793d23963725f002794dae4b13 大小:27143
2025-05-25 00:11:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:11:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:16:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:16:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:21:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:21:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:26:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:26:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:31:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:31:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:36:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:36:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:41:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:41:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:46:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:46:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:51:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:51:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:56:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 00:56:05 | DEBUG | 群成员变化检查完成
2025-05-25 00:58:15 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-05-25 00:58:15 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title><![CDATA[主人，我可以自己来.pdf]]></title>
		<type>74</type>
		<showtype>0</showtype>
		<appattach>
			<totallen>27522687</totallen>
			<fileext><![CDATA[pdf]]></fileext>
			<fileuploadtoken>v1_ZREsStdMd6Vi2N3P19u3SlY6zcfOQuFT/QvFTuauYLJ1nvyh7BcyYZTunMcjk7YE1YHeB0CWJmqqcrtI1dO/fQLWcsb7Ld6Yjb9r3cjSmmIgwfCG4wmWxDkqs6xWqAzzBI0Md69IM9F76U0qzXq31ViApOrDi9CzdJAMJzeSyD/+1pnnLP8PJEmQWn6ulqGQU4i7NBuCCZXUqg/MKPJiOqm93lP0l7v8</fileuploadtoken>
			<status>0</status>
		</appattach>
		<md5><![CDATA[57801d2da84ded7b70b15b51958571ab]]></md5>
		<laninfo><![CDATA[;bdfceeed88d3fc7e78b61ac6f3152e45]]></laninfo>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
</msg>

2025-05-25 00:58:15 | DEBUG | XML消息类型: 74
2025-05-25 00:58:15 | DEBUG | XML消息标题: 主人，我可以自己来.pdf
2025-05-25 00:58:15 | DEBUG | 附件信息 totallen: 27522687
2025-05-25 00:58:15 | DEBUG | 附件信息 fileext: pdf
2025-05-25 00:58:15 | DEBUG | 附件信息 fileuploadtoken: v1_ZREsStdMd6Vi2N3P19u3SlY6zcfOQuFT/QvFTuauYLJ1nvyh7BcyYZTunMcjk7YE1YHeB0CWJmqqcrtI1dO/fQLWcsb7Ld6Yjb9r3cjSmmIgwfCG4wmWxDkqs6xWqAzzBI0Md69IM9F76U0qzXq31ViApOrDi9CzdJAMJzeSyD/+1pnnLP8PJEmQWn6ulqGQU4i7NBuCCZXUqg/MKPJiOqm93lP0l7v8
2025-05-25 00:58:15 | DEBUG | 附件信息 status: 0
2025-05-25 00:58:17 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-05-25 00:58:17 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>主人，我可以自己来.pdf</title>
		<des />
		<username />
		<action>view</action>
		<type>6</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid>@cdn_3057020100044b30490201000204451128d302034c53d902042bd0e979020468313b55042434333864646666312d633634662d343632312d393063392d6463393335653338363734630204052400070201000405004c53d900_cfbc0a0c9ef74d0a2e39a2f945b8fc9f_1</attachid>
			<cdnattachurl>3057020100044b30490201000204451128d302034c53d902042bd0e979020468313b55042434333864646666312d633634662d343632312d393063392d6463393335653338363734630204052400070201000405004c53d900</cdnattachurl>
			<totallen>27522687</totallen>
			<aeskey>cfbc0a0c9ef74d0a2e39a2f945b8fc9f</aeskey>
			<encryver>1</encryver>
			<fileext>pdf</fileext>
			<islargefilemsg>1</islargefilemsg>
			<overwrite_newmsgid>8787874910280127920</overwrite_newmsgid>
			<fileuploadtoken><![CDATA[v1_ZREsStdMd6Vi2N3P19u3SlY6zcfOQuFT/QvFTuauYLJ1nvyh7BcyYZTunMcjk7YE1YHeB0CWJmqqcrtI1dO/fQLWcsb7Ld6Yjb9r3cjSmmIgwfCG4wmWxDkqs6xWqAzzBI0Md69IM9F76U0qzXq31ViApOrDi9CzdJAMJzeSyD/+1pnnLP8PJEmQWn6ulqGQU4i7NBuCCZXUqg/MKPJiOqm93lP0l7v8]]></fileuploadtoken>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>57801d2da84ded7b70b15b51958571ab</md5>
		<websearch />
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-05-25 00:58:17 | DEBUG | XML消息类型: 6
2025-05-25 00:58:17 | DEBUG | XML消息标题: 主人，我可以自己来.pdf
2025-05-25 00:58:17 | DEBUG | XML消息描述: None
2025-05-25 00:58:17 | DEBUG | 附件信息 attachid: @cdn_3057020100044b30490201000204451128d302034c53d902042bd0e979020468313b55042434333864646666312d633634662d343632312d393063392d6463393335653338363734630204052400070201000405004c53d900_cfbc0a0c9ef74d0a2e39a2f945b8fc9f_1
2025-05-25 00:58:17 | DEBUG | 附件信息 cdnattachurl: 3057020100044b30490201000204451128d302034c53d902042bd0e979020468313b55042434333864646666312d633634662d343632312d393063392d6463393335653338363734630204052400070201000405004c53d900
2025-05-25 00:58:17 | DEBUG | 附件信息 totallen: 27522687
2025-05-25 00:58:17 | DEBUG | 附件信息 aeskey: cfbc0a0c9ef74d0a2e39a2f945b8fc9f
2025-05-25 00:58:17 | DEBUG | 附件信息 encryver: 1
2025-05-25 00:58:17 | DEBUG | 附件信息 fileext: pdf
2025-05-25 00:58:17 | DEBUG | 附件信息 islargefilemsg: 1
2025-05-25 00:58:17 | DEBUG | 附件信息 overwrite_newmsgid: 8787874910280127920
2025-05-25 00:58:17 | DEBUG | 附件信息 fileuploadtoken: v1_ZREsStdMd6Vi2N3P19u3SlY6zcfOQuFT/QvFTuauYLJ1nvyh7BcyYZTunMcjk7YE1YHeB0CWJmqqcrtI1dO/fQLWcsb7Ld6Yjb9r3cjSmmIgwfCG4wmWxDkqs6xWqAzzBI0Md69IM9F76U0qzXq31ViApOrDi9CzdJAMJzeSyD/+1pnnLP8PJEmQWn6ulqGQU4i7NBuCCZXUqg/MKPJiOqm93lP0l7v8
2025-05-25 00:58:17 | INFO | 收到文件消息: 消息ID:963403464 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29 文件名:主人，我可以自己来.pdf
2025-05-25 01:01:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:01:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:01:09 | INFO | 收到表情消息: 消息ID:408920227 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:228e5a729a8d69c842e6a3b873307bb2 大小:25251
2025-05-25 01:01:11 | INFO | 收到表情消息: 消息ID:455992755 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:2dd42ed2e97471b1b8a855cddaf4f603 大小:28069
2025-05-25 01:06:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:06:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:11:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:11:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:11:33 | INFO | 收到图片消息: 消息ID:110171691 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 XML:<?xml version="1.0"?><msg><img aeskey="d645809967940bf75e8daaeb65299ec2" encryver="1" cdnthumbaeskey="d645809967940bf75e8daaeb65299ec2" cdnthumburl="3057020100044b30490201000204e34ed01102032dcfbe02048d4cf82402046831fdcd042435303433376135362d363365622d346333312d613964372d303530363832303164636134020405290a020201000405004c54a200" cdnthumblength="3605" cdnthumbheight="120" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204e34ed01102032dcfbe02048d4cf82402046831fdcd042435303433376135362d363365622d346333312d613964372d303530363832303164636134020405290a020201000405004c54a200" length="26066" md5="32ed84283a4878c8dc74351ddc21e014" hevc_mid_size="26066" originsourcemd5="15c59ce09810dbf29b11c9ab8ec1d70e"><secHashInfoBase64>eyJwaGFzaCI6IjcwNTAzMDcwYzAwMDIwMDAiLCJwZHFoYXNoIjoiMzFkNzJiNTdhOTViYTlkYTgzY2IwM2FiNWJjYTUwYmI1M2E5MTJiMzQxZDIxNWU4NDFlYWM3OGY4NzBkODkzZCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-05-25 01:11:33 | INFO | [ImageEcho] 收到图片消息: 来自 48097389945@chatroom, 发送人 wxid_w0rik7wwsvcy22
2025-05-25 01:11:33 | DEBUG | [ImageEcho] 成功保存群 48097389945@chatroom 的图片数据
2025-05-25 01:11:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-05-25 01:16:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:16:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:21:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:21:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:26:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:26:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:31:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:31:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:36:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:36:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:41:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:41:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:46:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:46:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:51:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:51:05 | DEBUG | 群成员变化检查完成
2025-05-25 01:56:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 01:56:05 | DEBUG | 群成员变化检查完成
2025-05-25 02:01:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 02:01:07 | DEBUG | 群成员变化检查完成
2025-05-25 02:06:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 02:06:06 | DEBUG | 群成员变化检查完成
2025-05-25 02:11:03 | DEBUG | 开始检查群成员变化，当前监控 3 个群
2025-05-25 02:11:05 | DEBUG | 群成员变化检查完成
